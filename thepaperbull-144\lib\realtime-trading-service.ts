import { ref, push, set, remove, onValue, off, serverTimestamp } from 'firebase/database'
import { realtimeDb } from './firebase'
import userService from './user-service'
import { firestoreService } from './firestore-service'
import { Position, Order, Trade, AccountInfo, MarketData } from '@/types/trading'
import { notificationService } from './notification-service'

interface TradingState {
  positions: Position[]
  orders: Order[]
  trades: Trade[]
  accountInfo: AccountInfo | null
  marketData: Record<string, MarketData>
  isLoading: boolean
  error: string | null
}

class RealtimeTradingService {
  private state: TradingState = {
    positions: [],
    orders: [],
    trades: [],
    accountInfo: null,
    marketData: {},
    isLoading: false,
    error: null
  }

  private subscribers: ((state: TradingState) => void)[] = []
  private userId: string | null = null
  private unsubscribeFunctions: (() => void)[] = []
  private lastOrderTime: number = 0
  private readonly ORDER_COOLDOWN = 1000 // 1 second cooldown between orders
  private updateTimeout: NodeJS.Timeout | null = null

  constructor() {
    // Subscribe to user changes
    userService.subscribe((user) => {
      console.log('Realtime Trading Service - User state changed:', {
        hasUser: !!user,
        userId: user?.id,
        userStructure: user ? Object.keys(user) : null
      })

      if (user && user.id) {
        if (this.userId !== user.id) {
          console.log('Initializing realtime trading service for user:', user.id)
          this.initializeForUser(user.id)
        }
      } else {
        console.log('User logged out, cleaning up realtime trading service')
        this.cleanup()
      }
    })

    // Also check for immediate user availability
    const currentUser = userService.getUser()
    if (currentUser && currentUser.id && !this.userId) {
      console.log('Found existing user on startup, initializing:', currentUser.id)
      this.initializeForUser(currentUser.id)
    }
  }

  // Subscribe to state changes
  subscribe(callback: (state: TradingState) => void): () => void {
    this.subscribers.push(callback)
    callback(this.state) // Immediate callback with current state

    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // Get current state
  getState(): TradingState {
    return { ...this.state }
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.state))
  }

  // Debounced notification for frequent updates (like price changes)
  private notifySubscribersDebounced() {
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout)
    }

    this.updateTimeout = setTimeout(() => {
      this.notifySubscribers()
    }, 100) // 100ms debounce
  }

  private async initializeForUser(userId: string) {
    this.userId = userId
    this.state.isLoading = true
    this.notifySubscribers()

    try {
      // Initialize account info
      await this.initializeAccountInfo()

      // Set up real-time listeners
      this.setupRealtimeListeners()

      this.state.isLoading = false
      this.state.error = null
      this.notifySubscribers()

      console.log('Realtime trading service initialized for user:', userId)
    } catch (error) {
      console.error('Error initializing realtime trading service:', error)
      this.state.isLoading = false
      this.state.error = 'Failed to initialize trading service'
      this.notifySubscribers()
    }
  }

  private async initializeAccountInfo() {
    if (!this.userId) return

    const userBalance = userService.getUserBalance()
    this.state.accountInfo = {
      totalWalletBalance: userBalance,
      totalUnrealizedProfit: 0,
      totalMarginBalance: userBalance,
      totalPositionInitialMargin: 0,
      totalOpenOrderInitialMargin: 0,
      availableBalance: userBalance,
      maxWithdrawAmount: userBalance,
      updateTime: Date.now()
    }
  }

  private setupRealtimeListeners() {
    if (!this.userId) return

    // Listen to positions
    const positionsRef = ref(realtimeDb, `users/${this.userId}/positions`)
    const positionsUnsubscribe = onValue(positionsRef, (snapshot) => {
      const data = snapshot.val()
      this.state.positions = data ? Object.entries(data).map(([id, pos]: [string, any]) => ({
        id,
        ...pos,
        timestamp: pos.timestamp || Date.now()
      })) : []
      
      this.updateAccountInfo()
      this.notifySubscribers()
    })

    // Listen to orders
    const ordersRef = ref(realtimeDb, `users/${this.userId}/orders`)
    const ordersUnsubscribe = onValue(ordersRef, (snapshot) => {
      const data = snapshot.val()
      this.state.orders = data ? Object.entries(data).map(([id, order]: [string, any]) => ({
        id,
        ...order,
        timestamp: order.timestamp || Date.now()
      })) : []
      
      this.updateAccountInfo()
      this.notifySubscribers()
    })

    // Listen to trades
    const tradesRef = ref(realtimeDb, `users/${this.userId}/trades`)
    const tradesUnsubscribe = onValue(tradesRef, (snapshot) => {
      const data = snapshot.val()
      this.state.trades = data ? Object.entries(data).map(([id, trade]: [string, any]) => ({
        id,
        ...trade,
        timestamp: trade.timestamp || Date.now()
      })).sort((a, b) => b.timestamp - a.timestamp) : []
      
      this.notifySubscribers()
    })

    this.unsubscribeFunctions = [
      () => off(positionsRef, 'value', positionsUnsubscribe),
      () => off(ordersRef, 'value', ordersUnsubscribe),
      () => off(tradesRef, 'value', tradesUnsubscribe)
    ]
  }

  private updateAccountInfo() {
    if (!this.state.accountInfo) return

    const userBalance = userService.getUserBalance()

    // Calculate total PnL from positions
    const totalPnL = this.state.positions.reduce((sum, pos) => sum + (pos.pnl || 0), 0)

    // Calculate total margin used by positions
    const totalMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)

    // Calculate total order margin (for pending orders) - more accurate calculation
    const totalOrderMargin = this.state.orders
      .filter(order => order.status === 'NEW')
      .reduce((sum, order) => {
        // Use current market price if available, otherwise use order price
        const currentPrice = this.state.marketData[order.symbol]?.price || order.price
        const orderValue = order.quantity * currentPrice
        return sum + (orderValue / (order.leverage || 10))
      }, 0)

    // Calculate available balance more conservatively
    const totalUsedMargin = totalMargin + totalOrderMargin
    const availableBalance = Math.max(0, userBalance - totalUsedMargin)

    // Update account info
    this.state.accountInfo.totalWalletBalance = userBalance
    this.state.accountInfo.totalUnrealizedProfit = totalPnL
    this.state.accountInfo.totalPositionInitialMargin = totalMargin
    this.state.accountInfo.totalOpenOrderInitialMargin = totalOrderMargin
    this.state.accountInfo.totalMarginBalance = userBalance + totalPnL
    this.state.accountInfo.availableBalance = availableBalance
    this.state.accountInfo.maxWithdrawAmount = availableBalance
    this.state.accountInfo.updateTime = Date.now()

    console.log('Account Info Updated:', {
      userBalance,
      totalMargin,
      totalOrderMargin,
      totalUsedMargin,
      availableBalance,
      positionsCount: this.state.positions.length,
      ordersCount: this.state.orders.filter(o => o.status === 'NEW').length
    })
  }

  // Update market data and recalculate PnL (optimized - no database writes)
  updateMarketData(symbol: string, data: Partial<MarketData>): void {
    this.state.marketData[symbol] = { ...this.state.marketData[symbol], ...data }

    // Update position PnL based on new market data (LOCAL ONLY - no database writes)
    if (data.price) {
      this.state.positions = this.state.positions.map(position => {
        if (position.symbol === symbol) {
          return this.updatePositionPnL(position, data.price!)
        }
        return position
      })

      // Only update account info and notify subscribers - NO database writes
      this.updateAccountInfo()
      this.notifySubscribersDebounced() // Use debounced notifications for price updates
    }
  }

  // Calculate PnL for a position based on current market price
  private calculatePnL(position: Position, currentPrice: number): { pnl: number; pnlPercent: number } {
    // Calculate price difference based on position side
    const priceDiff = position.side === 'LONG'
      ? currentPrice - position.entryPrice
      : position.entryPrice - currentPrice

    // PnL = price difference * position size (already accounts for leverage in size calculation)
    const pnl = priceDiff * position.size

    // PnL percentage based on margin invested
    const pnlPercent = position.margin > 0 ? (pnl / position.margin) * 100 : 0

    return {
      pnl: Number(pnl.toFixed(2)),
      pnlPercent: Number(pnlPercent.toFixed(2))
    }
  }

  // Update position PnL based on current market price
  private updatePositionPnL(position: Position, currentPrice: number): Position {
    const { pnl, pnlPercent } = this.calculatePnL(position, currentPrice)

    return {
      ...position,
      markPrice: currentPrice,
      pnl,
      pnlPercent
    }
  }

  // Calculate liquidation price
  private calculateLiquidationPrice(entryPrice: number, side: 'LONG' | 'SHORT', leverage: number): number {
    const maintenanceMarginRate = 0.005 // 0.5%
    const liquidationBuffer = 1 - maintenanceMarginRate - (1 / leverage)

    if (side === 'LONG') {
      return entryPrice * liquidationBuffer
    } else {
      return entryPrice * (2 - liquidationBuffer)
    }
  }

  // Place a new order with immediate UI feedback
  async placeOrder(orderRequest: {
    symbol: string
    side: 'BUY' | 'SELL'
    type: 'MARKET' | 'LIMIT'
    quantity: number
    price?: number
    leverage?: number
    stopLoss?: number
    takeProfit?: number
  }): Promise<string> {
    // Enhanced authentication check with fallback
    if (!this.userId) {
      // Try to get user ID from Firebase Auth as fallback
      const firebaseUser = userService.getFirebaseUser()
      const userServiceUser = userService.getUser()

      console.log('Authentication check in placeOrder:', {
        realtimeServiceUserId: this.userId,
        firebaseUser: firebaseUser ? { uid: firebaseUser.uid, email: firebaseUser.email } : null,
        userServiceUser: userServiceUser ? { id: userServiceUser.id, email: userServiceUser.email } : null
      })

      if (firebaseUser) {
        console.log('Using Firebase user ID as fallback:', firebaseUser.uid)
        this.userId = firebaseUser.uid
        // Initialize for this user
        await this.initializeForUser(firebaseUser.uid)
      } else if (userServiceUser && userServiceUser.id) {
        console.log('Using user service ID as fallback:', userServiceUser.id)
        this.userId = userServiceUser.id
        await this.initializeForUser(userServiceUser.id)
      } else {
        console.error('Authentication failed - no user ID available')
        throw new Error('User not authenticated. Please sign in and try again.')
      }
    }

    // Check order cooldown to prevent rapid-fire orders
    const now = Date.now()
    if (now - this.lastOrderTime < this.ORDER_COOLDOWN) {
      const remainingTime = this.ORDER_COOLDOWN - (now - this.lastOrderTime)
      throw new Error(`Please wait ${Math.ceil(remainingTime / 1000)} second(s) before placing another order`)
    }

    // Get current market price
    const currentPrice = this.state.marketData[orderRequest.symbol]?.price || orderRequest.price || 0
    if (currentPrice <= 0) {
      throw new Error('Invalid market price. Please try again.')
    }

    // Calculate order requirements
    const orderValue = orderRequest.quantity * currentPrice
    const requiredMargin = orderValue / (orderRequest.leverage || 10)
    const commission = orderValue * 0.001 // 0.1% commission

    // Get current account state and validate balance more strictly
    const userBalance = userService.getUserBalance()
    const currentMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)
    const pendingOrderMargin = this.state.orders
      .filter(order => order.status === 'NEW')
      .reduce((sum, order) => {
        const price = this.state.marketData[order.symbol]?.price || order.price
        const value = order.quantity * price
        return sum + (value / (order.leverage || 10))
      }, 0)

    const totalUsedMargin = currentMargin + pendingOrderMargin
    const availableBalance = userBalance - totalUsedMargin
    const totalRequired = requiredMargin + commission

    console.log('Balance Validation:', {
      userBalance,
      currentMargin,
      pendingOrderMargin,
      totalUsedMargin,
      availableBalance,
      requiredMargin,
      commission,
      totalRequired,
      orderValue,
      leverage: orderRequest.leverage || 10
    })

    if (totalRequired > availableBalance) {
      throw new Error(`Insufficient balance. Required: ${totalRequired.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
    }

    // Additional safety check - prevent orders if balance is too low
    if (availableBalance < 10) { // Minimum 10 USDT required
      throw new Error('Insufficient balance. Minimum 10 USDT required for trading.')
    }

    const orderId = `ord_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create order object
    const order: Order = {
      id: orderId,
      symbol: orderRequest.symbol,
      side: orderRequest.side,
      type: orderRequest.type,
      quantity: orderRequest.quantity,
      price: orderRequest.price || currentPrice,
      status: 'NEW',
      timestamp: Date.now(),
      executedQty: 0,
      leverage: orderRequest.leverage || 10
    }

    // Add order to local state immediately for instant UI feedback
    this.state.orders.push(order)
    this.updateAccountInfo()
    this.notifySubscribers()

    // Update last order time
    this.lastOrderTime = now

    try {
      // Add order to Realtime Database
      const orderRef = ref(realtimeDb, `users/${this.userId}/orders/${orderId}`)
      await set(orderRef, {
        ...order,
        createdAt: serverTimestamp()
      })

      // For market orders, execute immediately
      if (orderRequest.type === 'MARKET') {
        await this.executeOrder(orderId, order, currentPrice)
      }

      // Show immediate notification
      notificationService.createTradeNotification(this.userId, 'order_placed', {
        symbol: orderRequest.symbol,
        side: orderRequest.side,
        type: orderRequest.type,
        price: orderRequest.price || currentPrice,
        quantity: orderRequest.quantity
      })

      return orderId
    } catch (error) {
      // Remove from local state if Firebase operation failed
      this.state.orders = this.state.orders.filter(o => o.id !== orderId)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw error
    }
  }

  // Execute an order (create position)
  private async executeOrder(orderId: string, order: Order, executionPrice: number): Promise<void> {
    if (!this.userId) return

    const commission = order.quantity * executionPrice * 0.001
    const margin = (order.quantity * executionPrice) / order.leverage

    // Final balance validation before execution using cached data
    const userBalance = userService.getUser()?.balance?.current || userService.getUserBalance()
    const currentMargin = this.state.positions.reduce((sum, pos) => sum + (pos.margin || 0), 0)
    const availableBalance = userBalance - currentMargin
    const totalRequired = margin + commission

    console.log('Execution Balance Check:', {
      userBalance,
      currentMargin,
      availableBalance,
      margin,
      commission,
      totalRequired,
      orderId
    })

    if (totalRequired > availableBalance) {
      // Remove the order from local state
      this.state.orders = this.state.orders.filter(o => o.id !== orderId)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw new Error(`Execution failed: Insufficient balance. Required: ${totalRequired.toFixed(2)} USDT, Available: ${availableBalance.toFixed(2)} USDT`)
    }

    const positionId = `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create position object
    const position: Position = {
      id: positionId,
      symbol: order.symbol,
      side: order.side === 'BUY' ? 'LONG' : 'SHORT',
      entryPrice: executionPrice,
      markPrice: executionPrice,
      size: order.quantity,
      margin: margin,
      leverage: order.leverage,
      pnl: 0,
      pnlPercent: 0,
      liquidationPrice: this.calculateLiquidationPrice(
        executionPrice,
        order.side === 'BUY' ? 'LONG' : 'SHORT',
        order.leverage
      ),
      timestamp: Date.now(),
      orderId: orderId
    }

    // Add position to local state immediately
    this.state.positions.push(position)

    // Update order status
    const orderIndex = this.state.orders.findIndex(o => o.id === orderId)
    if (orderIndex !== -1) {
      this.state.orders[orderIndex].status = 'FILLED'
      this.state.orders[orderIndex].executedQty = order.quantity
    }

    this.updateAccountInfo()
    this.notifySubscribers()

    // Show immediate notification
    notificationService.createTradeNotification(this.userId, 'position_opened', {
      symbol: order.symbol,
      side: position.side,
      size: order.quantity,
      entryPrice: executionPrice
    })

    try {
      // Save to Realtime Database in parallel
      const positionRef = ref(realtimeDb, `users/${this.userId}/positions/${positionId}`)
      const orderRef = ref(realtimeDb, `users/${this.userId}/orders/${orderId}`)

      await Promise.all([
        set(positionRef, {
          ...position,
          createdAt: serverTimestamp()
        }),
        set(orderRef, {
          ...this.state.orders[orderIndex],
          updatedAt: serverTimestamp()
        })
      ])

      // Update user balance (deduct commission)
      const currentBalance = userService.getUserBalance()
      await userService.updateBalance(
        currentBalance - commission,
        'commission',
        `Trading commission: ${commission.toFixed(2)} USDT`
      )

      // Also save to Firestore for historical records
      firestoreService.addPosition(this.userId, position).catch(console.error)

    } catch (error) {
      console.error('Error saving position to Firebase:', error)
      // Position is already in local state, so user sees it immediately
      // Firebase sync will happen eventually
    }
  }

  // Close a position with immediate UI feedback
  async closePosition(positionId: string): Promise<void> {
    if (!this.userId) return

    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)
    if (positionIndex === -1) return

    const position = this.state.positions[positionIndex]
    const currentPrice = this.state.marketData[position.symbol]?.price || position.markPrice
    const commission = position.size * currentPrice * 0.001

    // Remove position from local state immediately
    this.state.positions.splice(positionIndex, 1)
    this.updateAccountInfo()
    this.notifySubscribers()

    // Show immediate notification
    notificationService.createTradeNotification(this.userId, 'position_closed', {
      symbol: position.symbol,
      side: position.side,
      pnl: position.pnl,
      closePrice: currentPrice
    })

    try {
      // Create trade record
      const tradeId = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const trade: Trade = {
        id: tradeId,
        symbol: position.symbol,
        side: position.side === 'LONG' ? 'SELL' : 'BUY',
        price: currentPrice,
        quantity: position.size,
        commission: commission,
        realizedPnl: position.pnl,
        timestamp: Date.now(),
        leverage: position.leverage,
        orderId: position.orderId || '',
        positionId: positionId
      }

      // Add trade to local state
      this.state.trades.unshift(trade)
      this.notifySubscribers()

      // Remove from Realtime Database and add trade
      const positionRef = ref(realtimeDb, `users/${this.userId}/positions/${positionId}`)
      const tradeRef = ref(realtimeDb, `users/${this.userId}/trades/${tradeId}`)

      await Promise.all([
        remove(positionRef),
        set(tradeRef, {
          ...trade,
          createdAt: serverTimestamp()
        })
      ])

      // Update user balance
      const currentBalance = userService.getUserBalance()
      await userService.updateBalance(
        currentBalance + position.pnl - commission,
        position.pnl > 0 ? 'trade_profit' : 'trade_loss',
        `Position closed: ${position.pnl > 0 ? '+' : ''}${position.pnl.toFixed(2)} USDT`
      )

      // Also save to Firestore for historical records
      firestoreService.addTrade(this.userId, trade).catch(console.error)

    } catch (error) {
      console.error('Error closing position in Firebase:', error)
      // Re-add position to local state if Firebase operation failed
      this.state.positions.push(position)
      this.updateAccountInfo()
      this.notifySubscribers()
      throw error
    }
  }

  // Get current state
  getState(): TradingState {
    return { ...this.state }
  }

  // Get market data for a symbol
  getMarketData(symbol: string): MarketData | null {
    return this.state.marketData[symbol] || null
  }

  private cleanup() {
    this.unsubscribeFunctions.forEach(unsubscribe => unsubscribe())
    this.unsubscribeFunctions = []

    // Clear any pending timeouts
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout)
      this.updateTimeout = null
    }

    this.userId = null
    this.state = {
      positions: [],
      orders: [],
      trades: [],
      accountInfo: null,
      marketData: {},
      isLoading: false,
      error: null
    }
    this.notifySubscribers()
  }
}

// Export singleton instance
export const realtimeTradingService = new RealtimeTradingService()
export default realtimeTradingService

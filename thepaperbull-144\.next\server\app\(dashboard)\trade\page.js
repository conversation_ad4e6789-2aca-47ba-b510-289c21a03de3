(()=>{var e={};e.id=871,e.ids=[871],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14360:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\trade\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\trade\\page.tsx","default")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33196:(e,r,t)=>{Promise.resolve().then(t.bind(t,14360))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55090:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>i});var s=t(65239),a=t(48088),d=t(88170),o=t.n(d),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let i={children:["",{children:["(dashboard)",{children:["trade",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,14360)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\trade\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,75870))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"D:\\step-by-step\\thepaperbull-144\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"D:\\step-by-step\\thepaperbull-144\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,75870))).default(e)],manifest:void 0}}]}.children,c=["D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\trade\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/trade/page",pathname:"/trade",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},89712:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>R});var s=t(60687),a=t(43210),d=t(25878);let o=[{symbol:"BTCUSDT",baseAsset:"BTC",displayName:"Bitcoin",category:"major"},{symbol:"ETHUSDT",baseAsset:"ETH",displayName:"Ethereum",category:"major"},{symbol:"BNBUSDT",baseAsset:"BNB",displayName:"BNB",category:"major"},{symbol:"XRPUSDT",baseAsset:"XRP",displayName:"XRP",category:"altcoin"},{symbol:"SOLUSDT",baseAsset:"SOL",displayName:"Solana",category:"altcoin"},{symbol:"ADAUSDT",baseAsset:"ADA",displayName:"Cardano",category:"altcoin"},{symbol:"TRXUSDT",baseAsset:"TRX",displayName:"Tron",category:"altcoin"},{symbol:"SUIUSDT",baseAsset:"SUI",displayName:"Sui",category:"altcoin"},{symbol:"LINKUSDT",baseAsset:"LINK",displayName:"Chainlink",category:"altcoin"},{symbol:"AVAXUSDT",baseAsset:"AVAX",displayName:"Avalanche",category:"altcoin"},{symbol:"XLMUSDT",baseAsset:"XLM",displayName:"Stellar",category:"altcoin"},{symbol:"HBARUSDT",baseAsset:"HBAR",displayName:"Hedera",category:"altcoin"},{symbol:"TONUSDT",baseAsset:"TON",displayName:"Toncoin",category:"altcoin"},{symbol:"DOTUSDT",baseAsset:"DOT",displayName:"Polkadot",category:"altcoin"},{symbol:"LTCUSDT",baseAsset:"LTC",displayName:"Litecoin",category:"altcoin"},{symbol:"XMRUSDT",baseAsset:"XMR",displayName:"Monero",category:"altcoin"},{symbol:"NEARUSDT",baseAsset:"NEAR",displayName:"Near Protocol",category:"altcoin"},{symbol:"APTUSDT",baseAsset:"APT",displayName:"Aptos",category:"altcoin"},{symbol:"ICPUSDT",baseAsset:"ICP",displayName:"Internet Computer",category:"altcoin"},{symbol:"ARUSDT",baseAsset:"AR",displayName:"Arweave",category:"altcoin"},{symbol:"DOGEUSDT",baseAsset:"DOGE",displayName:"Dogecoin",category:"meme"},{symbol:"SHIBUSDT",baseAsset:"SHIB",displayName:"Shiba Inu",category:"meme"},{symbol:"PEPEUSDT",baseAsset:"PEPE",displayName:"Pepe",category:"meme"},{symbol:"UNIUSDT",baseAsset:"UNI",displayName:"Uniswap",category:"defi"},{symbol:"ONDOUSDT",baseAsset:"ONDO",displayName:"Ondo",category:"defi"}],l=e=>{let r=o.find(r=>r.symbol===e);return r?.displayName||e.replace("USDT","")},n=e=>{let r=e.replace("USDT","");return`BINANCE:${r}USDT.P`};function i({symbol:e="BTCUSDT",interval:r="15",height:t="100%",containerClassName:o=""}){let{resolvedTheme:l}=(0,d.D)(),i=(0,a.useRef)(null);return(0,a.useRef)(!1),n(e),(0,s.jsx)("div",{ref:i,className:`tradingview-widget-container w-full h-full rounded-lg border border-border overflow-hidden ${o}`,style:{height:t}})}var c=t(3589),m=t(78272),x=t(99891),u=t(47155),h=t(52581);function p({symbol:e,lastPrice:r,leverage:t,onOrderSubmit:d}){let{placeOrder:o,isLoading:l,accountInfo:n,getAvailableBalance:i}=(0,u.fx)(),[p,b]=(0,a.useState)("MARKET"),[f,g]=(0,a.useState)("BUY"),[y,N]=(0,a.useState)(""),[j,v]=(0,a.useState)(r),[w,S]=(0,a.useState)([25,50,75,100]),[k,P]=(0,a.useState)(""),[T,A]=(0,a.useState)(""),[C,F]=(0,a.useState)(0),[U,D]=(0,a.useState)(0),[L,R]=(0,a.useState)(!1),$=t||10,E=n?.totalWalletBalance||1e4,M=i();if(null==M||0===M){let e=n?.totalUnrealizedProfit||0;M=Math.max(0,E-(n?.totalPositionInitialMargin||0)+e)}let I=()=>!r||M<=0?"0":(M*$/Number.parseFloat(r)).toFixed(4),B=e=>{N((Number.parseFloat(I())*e/100).toFixed(4))},O=(e,r)=>{if(!j)return;let t=Number.parseFloat(j);"sl"===r?(F(e),"BUY"===f?P((t*(1-e/100)).toFixed(2)):P((t*(1+e/100)).toFixed(2))):(D(e),"BUY"===f?A((t*(1+e/100)).toFixed(2)):A((t*(1-e/100)).toFixed(2)))},q=async t=>{if(t.preventDefault(),!y||0>=Number.parseFloat(y)){h.oR.error("Please enter a valid quantity");return}if("LIMIT"===p&&(!j||0>=Number.parseFloat(j))){h.oR.error("Please enter a valid price");return}let s="MARKET"===p?Number.parseFloat(r):Number.parseFloat(j),a=Number.parseFloat(y)*s/$;if(a>M){h.oR.error(`Insufficient balance. Required: ${a.toFixed(2)} USDT, Available: ${M.toFixed(2)} USDT`);return}let l={symbol:e,side:f,type:p,quantity:Number.parseFloat(y),leverage:$};"LIMIT"===p&&(l.price=Number.parseFloat(j)),k&&(l.stopLoss=Number.parseFloat(k)),T&&(l.takeProfit=Number.parseFloat(T));try{console.log("Submitting order from form:",l);let t=await o(l);console.log("Order submitted successfully with ID:",t),d&&d(l),N(""),"LIMIT"===p&&v(r),P(""),A(""),F(0),D(0),h.oR.success(`${f} order placed successfully`,{description:`${y} ${e} at ${"MARKET"===p?"market price":`$${j}`}`,duration:5e3})}catch(r){console.error("Error submitting order:",r);let e=r instanceof Error?r.message:"Failed to submit order. Please try again.";h.oR.error(e,{duration:8e3,description:"Please check your connection and try again."})}},z=(()=>{if(!y||!j||!k&&!T)return{sl:"0",tp:"0"};let e=Number.parseFloat(y),r=Number.parseFloat(j),t=k?Number.parseFloat(k):0,s=T?Number.parseFloat(T):0,a=0,d=0;return"BUY"===f?(t&&(a=(t-r)*e),s&&(d=(s-r)*e)):(t&&(a=(r-t)*e),s&&(d=(r-s)*e)),{sl:a.toFixed(2),tp:d.toFixed(2)}})(),W="MARKET"===p?Number.parseFloat(r):Number.parseFloat(j),H=Number.parseFloat(y||"0")*W,Y=H/$,_=Y<=M;return(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border/50 shadow-lg p-4 h-full backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-lg font-bold text-foreground tracking-tight",children:"Place Order"}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-bold text-emerald-600 text-sm",children:[M.toFixed(2)," USDT"]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Available"})]})]})}),(0,s.jsxs)("form",{onSubmit:q,className:"flex flex-col h-[calc(100%-80px)]",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Order Type"}),(0,s.jsxs)("div",{className:"flex rounded-md overflow-hidden border border-border/50 bg-muted/20",children:[(0,s.jsx)("button",{type:"button",className:`flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ${"MARKET"===p?"bg-primary text-primary-foreground shadow-sm":"bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"}`,onClick:()=>b("MARKET"),children:"Market"}),(0,s.jsx)("button",{type:"button",className:`flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ${"LIMIT"===p?"bg-primary text-primary-foreground shadow-sm":"bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"}`,onClick:()=>b("LIMIT"),children:"Limit"})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-2",children:"Order Side"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)("button",{type:"button",className:`py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ${"BUY"===f?"bg-emerald-600 text-white shadow-md shadow-emerald-600/25":"bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-100 dark:hover:bg-emerald-950/40"}`,onClick:()=>g("BUY"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:"↗"}),(0,s.jsx)("span",{children:"BUY"})]})}),(0,s.jsx)("button",{type:"button",className:`py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ${"SELL"===f?"bg-red-600 text-white shadow-md shadow-red-600/25":"bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-950/40"}`,onClick:()=>g("SELL"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:"↘"}),(0,s.jsx)("span",{children:"SELL"})]})})]})]}),"LIMIT"===p&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Limit Price"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"number",value:j,onChange:e=>v(e.target.value),className:"w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm pr-14 font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200",step:"0.01",min:"0",required:!0,placeholder:"Enter price"}),(0,s.jsxs)("div",{className:"absolute right-1.5 top-1/2 transform -translate-y-1/2 flex border border-border/50 rounded-sm overflow-hidden",children:[(0,s.jsx)("button",{type:"button",className:"bg-muted/50 p-1 hover:bg-muted transition-colors",onClick:()=>v((Number.parseFloat(j)+.01).toString()),children:(0,s.jsx)(c.A,{className:"h-2.5 w-2.5"})}),(0,s.jsx)("button",{type:"button",className:"bg-muted/50 p-1 hover:bg-muted transition-colors border-l border-border/50",onClick:()=>v(Math.max(.01,Number.parseFloat(j)-.01).toString()),children:(0,s.jsx)(m.A,{className:"h-2.5 w-2.5"})})]})]}),(0,s.jsxs)("div",{className:"mt-1.5 text-xs text-muted-foreground",children:["Market: ",(0,s.jsx)("span",{className:"font-medium text-foreground",children:r})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Quantity"}),(0,s.jsx)("input",{type:"number",value:y,onChange:e=>N(e.target.value),className:"w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200",step:"0.0001",min:"0",required:!0,placeholder:"Enter quantity"}),(0,s.jsx)("div",{className:"flex gap-1.5 mt-2",children:w.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>B(e),className:"flex-1 text-xs bg-muted/50 border border-border/50 px-2 py-1.5 rounded-md hover:bg-muted hover:border-border font-medium transition-all duration-200",children:[e,"%"]},e))}),(0,s.jsxs)("div",{className:"mt-1.5 text-xs text-muted-foreground",children:["Max: ",(0,s.jsx)("span",{className:"font-medium text-foreground",children:I()})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-2.5 border border-border/50 rounded-lg cursor-pointer mb-2 hover:border-border transition-all duration-200 bg-muted/20",onClick:()=>R(!L),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-3.5 w-3.5 mr-2 text-primary"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"SL & TP"})]}),(0,s.jsx)(m.A,{className:`h-3.5 w-3.5 transition-transform duration-200 ${L?"rotate-180":""}`})]}),L&&(0,s.jsxs)("div",{className:"p-3 border border-border/30 rounded-lg bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1.5",children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-foreground",children:"Stop Loss"}),(0,s.jsxs)("span",{className:`text-xs font-bold ${Number.parseFloat(z.sl)>=0?"text-emerald-500":"text-red-500"}`,children:[z.sl," USDT"]})]}),(0,s.jsx)("input",{type:"number",value:k,onChange:e=>P(e.target.value),className:"w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-red-400 focus:ring-1 focus:ring-red-400/20 transition-all duration-200 mb-1.5",step:"0.01",min:"0",placeholder:"BUY"===f?"Lower than entry":"Higher than entry"}),(0,s.jsx)("div",{className:"flex gap-1.5",children:[1,2,5,10].map(e=>(0,s.jsxs)("button",{type:"button",className:`flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ${C===e?"bg-red-500 text-white shadow-sm":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"}`,onClick:()=>O(e,"sl"),children:["-",e,"%"]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1.5",children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-foreground",children:"Take Profit"}),(0,s.jsxs)("span",{className:`text-xs font-bold ${Number.parseFloat(z.tp)>=0?"text-emerald-500":"text-red-500"}`,children:[z.tp," USDT"]})]}),(0,s.jsx)("input",{type:"number",value:T,onChange:e=>A(e.target.value),className:"w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400/20 transition-all duration-200 mb-1.5",step:"0.01",min:"0",placeholder:"BUY"===f?"Higher than entry":"Lower than entry"}),(0,s.jsx)("div",{className:"flex gap-1.5",children:[1,2,5,10].map(e=>(0,s.jsxs)("button",{type:"button",className:`flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ${U===e?"bg-emerald-500 text-white shadow-sm":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"}`,onClick:()=>O(e,"tp"),children:["+",e,"%"]},e))})]})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-muted/60 to-muted/40 p-3 rounded-lg border border-border/30 mb-4",children:[(0,s.jsx)("h3",{className:"text-xs font-bold text-foreground mb-2",children:"Order Summary"}),(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Value:"}),(0,s.jsxs)("span",{className:"font-semibold text-foreground text-sm",children:[H.toFixed(2)," USDT"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Margin:"}),(0,s.jsxs)("span",{className:`font-semibold text-sm ${_?"text-foreground":"text-red-500"}`,children:[Y.toFixed(2)," USDT"]})]}),!_&&y&&(0,s.jsxs)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-md",children:[(0,s.jsx)("div",{className:"text-red-600 dark:text-red-400 text-xs font-semibold",children:"⚠️ Insufficient Balance"}),(0,s.jsxs)("div",{className:"text-red-500 dark:text-red-400 text-xs mt-0.5",children:["Need ",(Y-M).toFixed(2)," USDT more"]})]})]})]}),(0,s.jsx)("button",{type:"submit",disabled:l||!_,className:`w-full py-4 rounded-xl font-bold text-base mt-auto transition-all duration-300 transform ${"BUY"===f?"bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 shadow-lg shadow-emerald-600/25":"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg shadow-red-600/25"} text-white ${l||!_?"opacity-70 cursor-not-allowed":"hover:scale-105 hover:shadow-xl active:scale-95"}`,children:l?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Processing Order..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("span",{className:"text-xl mr-2",children:"BUY"===f?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,s.jsx)("span",{className:"text-lg font-bold",children:`${"BUY"===f?"BUY / LONG":"SELL / SHORT"} ${e.replace("USDT","")}`})]})})]})]})}var b=t(87891),f=t(62688);let g=(0,f.A)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),y=(0,f.A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var N=t(11860),j=t(99270),v=t(64398),w=t(6955),S=t(16189);function k(){let{user:e,loading:r}=(0,w.A)(),[t,d]=(0,a.useState)(null),[o,l]=(0,a.useState)(!0);return((0,S.useRouter)(),r||o)?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Checking..."})]}):e?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsxs)("span",{className:"text-xs text-green-600 dark:text-green-400",children:[t?.balance?.current||1e4," USDT"]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,s.jsx)("span",{className:"text-xs text-red-600 dark:text-red-400",children:"Not Auth"})]})}function P({symbol:e,onSymbolChange:r,leverage:t=10,onLeverageChange:d,isCrossMargin:o=!0,onCrossMarginChange:n,connectionStatus:i="connected",onRetryConnection:x}){let[u,h]=(0,a.useState)("0"),[p,f]=(0,a.useState)("0"),[w,S]=(0,a.useState)(!0),[P,T]=(0,a.useState)(!1),[A,C]=(0,a.useState)(!1),[F,U]=(0,a.useState)(""),[D,L]=(0,a.useState)([]),[R,$]=(0,a.useState)([]),[E,M]=(0,a.useState)([]),[I,B]=(0,a.useState)("all"),[O,q]=(0,a.useState)(!1),[z,W]=(0,a.useState)(!1),H=(0,a.useRef)(null),Y=(0,a.useRef)(null),_=(0,a.useRef)(null),X=(0,a.useRef)(null),G=l(e),K=e.replace("USDT",""),V=()=>{switch(i){case"connected":default:return(0,s.jsx)(b.A,{className:"h-3 w-3"});case"connecting":return(0,s.jsx)(b.A,{className:"h-3 w-3 animate-pulse"});case"error":return(0,s.jsx)(g,{className:"h-3 w-3"})}},J=()=>{switch(i){case"connected":default:return"text-emerald-500";case"connecting":return"text-blue-500";case"error":return"text-amber-500"}},Q=e=>{r&&r(e),T(!1),U("")},Z=(e,r)=>{r.stopPropagation();let t=E.includes(e)?E.filter(r=>r!==e):[...E,e];M(t),localStorage.setItem("favoriteMarkets",JSON.stringify(t))},ee=()=>{U(""),_.current&&_.current.focus()};return(0,s.jsxs)("div",{className:"relative",ref:H,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between bg-card rounded-lg border border-border p-2 sm:p-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:"flex items-center cursor-pointer hover:bg-muted/50 rounded-md px-2 py-1 transition-colors",onClick:()=>T(!P),children:[(0,s.jsx)("span",{className:"text-base sm:text-lg font-bold mr-1",children:G}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground hidden sm:inline mr-1",children:[K,"/USDT"]}),(0,s.jsx)(m.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,s.jsxs)("div",{className:"flex items-center ml-3 sm:ml-4",children:[(0,s.jsx)("span",{className:"text-sm sm:text-base font-medium mr-2",children:u}),(0,s.jsxs)("span",{className:`flex items-center text-xs ${w?"text-emerald-500":"text-red-500"}`,children:[w?(0,s.jsx)(c.A,{className:"h-3 w-3 mr-0.5"}):(0,s.jsx)(m.A,{className:"h-3 w-3 mr-0.5"}),w?"+":"",p,"%"]}),(0,s.jsx)("div",{className:"sm:hidden ml-3",children:(0,s.jsx)(k,{})})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"hidden sm:block",children:(0,s.jsx)(k,{})}),(0,s.jsxs)("div",{className:"relative",ref:Y,children:[(0,s.jsxs)("button",{onClick:()=>C(!A),className:"flex items-center bg-muted/30 hover:bg-muted/50 rounded-md px-2 py-1 transition-colors border border-border/50",children:[(0,s.jsx)(y,{className:"h-3 w-3 mr-1 text-muted-foreground"}),(0,s.jsxs)("span",{className:"text-xs font-medium text-primary",children:[t,"x"]})]}),A&&(0,s.jsx)("div",{className:"absolute top-full right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold",children:"Adjust Leverage"}),(0,s.jsx)("button",{onClick:()=>C(!1),className:"text-muted-foreground hover:text-foreground",children:(0,s.jsx)(N.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[5,10,20,50,100].map(e=>(0,s.jsxs)("button",{onClick:()=>{d&&d(e),C(!1)},className:`py-2 text-xs font-semibold rounded-lg transition-all duration-200 ${t===e?"bg-primary text-primary-foreground shadow-md":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"}`,children:[e,"x"]},e))}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",min:"1",max:"125",step:"1",value:t,onChange:e=>{d&&d(Number.parseInt(e.target.value))},className:"w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider",style:{background:`linear-gradient(to right, hsl(var(--primary)) 0%, hsl(var(--primary)) ${(t-1)/124*100}%, hsl(var(--muted)) ${(t-1)/124*100}%, hsl(var(--muted)) 100%)`}}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,s.jsx)("span",{children:"1x"}),(0,s.jsxs)("span",{className:"font-medium text-primary",children:[t,"x"]}),(0,s.jsx)("span",{children:"125x"})]})]})]})})]}),(0,s.jsx)("button",{onClick:()=>{n&&n(!o)},className:`flex items-center rounded-md px-2 py-1 transition-colors border text-xs font-medium ${o?"bg-primary/10 border-primary/30 text-primary":"bg-muted/30 border-border/50 hover:bg-muted/50 text-muted-foreground"}`,children:"Cross"}),(0,s.jsxs)("div",{className:"relative",ref:X,children:[(0,s.jsx)("button",{onClick:()=>W(!z),className:`flex items-center rounded-md p-1 transition-colors hover:bg-muted/50 ${J()}`,children:V()}),z&&(0,s.jsx)("div",{className:"absolute top-full right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:J(),children:V()}),(0,s.jsx)("span",{className:"text-sm font-semibold",children:"Connection Status"})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mb-3",children:(()=>{switch(i){case"connected":default:return"Real-time data connected";case"connecting":return"Connecting to real-time data...";case"error":return"Using fallback price updates. Real-time data may be delayed."}})()}),"error"===i&&x&&(0,s.jsx)("button",{onClick:()=>{x(),W(!1)},className:"w-full text-xs bg-amber-500/20 hover:bg-amber-500/30 text-amber-600 px-3 py-2 rounded-md transition-colors",children:"Retry Connection"})]})})]})]})]}),P&&(0,s.jsxs)("div",{className:"absolute left-0 right-0 sm:left-0 sm:right-auto mt-2 w-full sm:w-80 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsxs)("div",{className:"p-2 border-b border-border",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{ref:_,type:"text",placeholder:"Search markets...",value:F,onChange:e=>U(e.target.value),className:"w-full pl-8 pr-8 py-1.5 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"}),(0,s.jsx)(j.A,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),F&&(0,s.jsx)("button",{onClick:ee,className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,s.jsxs)("div",{className:"flex mt-2 border border-border rounded-md overflow-hidden",children:[(0,s.jsx)("button",{className:`flex-1 py-1 text-xs font-medium ${"all"===I?"bg-primary text-primary-foreground":"bg-background"}`,onClick:()=>B("all"),children:"All Markets"}),(0,s.jsx)("button",{className:`flex-1 py-1 text-xs font-medium ${"favorites"===I?"bg-primary text-primary-foreground":"bg-background"}`,onClick:()=>B("favorites"),children:"Favorites"})]})]}),(0,s.jsx)("div",{className:"max-h-[350px] overflow-y-auto",children:O?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):0===R.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)("p",{className:"text-sm",children:"No markets found"}),F&&(0,s.jsx)("button",{className:"mt-2 text-xs text-primary hover:underline",onClick:ee,children:"Clear search"})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"grid grid-cols-[1fr,auto,auto] px-2 sm:px-3 py-1 border-b border-border text-xs text-muted-foreground",children:[(0,s.jsx)("div",{children:"Pair"}),(0,s.jsx)("div",{children:"Price"}),(0,s.jsx)("div",{className:"hidden sm:block",children:"24h"}),(0,s.jsx)("div",{className:"sm:hidden",children:"%"})]}),R.map(r=>(0,s.jsxs)("div",{className:`px-2 sm:px-3 py-2 hover:bg-muted cursor-pointer grid grid-cols-[1fr,auto,auto] items-center ${r.symbol===e?"bg-primary/10":""}`,onClick:()=>Q(r.symbol),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{onClick:e=>Z(r.symbol,e),className:"mr-1 focus:outline-none",children:(0,s.jsx)(v.A,{className:`h-3 w-3 ${E.includes(r.symbol)?"fill-yellow-400 text-yellow-400":"text-muted-foreground"}`})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium text-xs sm:text-sm",children:l(r.symbol)}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground hidden sm:block",children:r.symbol.replace("USDT","")})]})]}),(0,s.jsx)("div",{className:"text-right text-xs sm:text-sm",children:r.lastPrice}),(0,s.jsxs)("div",{className:`text-right text-xs ml-1 sm:ml-3 ${Number.parseFloat(r.priceChangePercent)>=0?"text-emerald-500":"text-red-500"}`,children:[Number.parseFloat(r.priceChangePercent)>=0?"+":"",r.priceChangePercent,"%"]})]},r.symbol))]})})]})]})}t(21643);var T=t(43649),A=t(13964),C=t(28947);function F(){let{positions:e,isLoading:r,error:t,closePosition:d,updatePosition:o}=(0,u.fx)(),[l,n]=(0,a.useState)(null),[i,c]=(0,a.useState)(""),[m,x]=(0,a.useState)(""),[p,b]=(0,a.useState)(new Set),f=async r=>{if(!p.has(r))try{let t=e.find(e=>e.id===r);if(!t){h.oR.info("Position already closed",{description:"This position has already been closed"});return}b(e=>new Set(e).add(r)),await d(r),h.oR.success("Position closed successfully",{description:`${t.symbol} ${t.side} position closed`})}catch(r){console.error("Error closing position:",r);let e=r instanceof Error?r.message:"Unknown error";e.includes("not found")||e.includes("already been closed")?h.oR.info("Position already closed",{description:"This position has already been closed"}):h.oR.error("Failed to close position. Please try again.")}finally{b(e=>{let t=new Set(e);return t.delete(r),t})}},g=e=>{n(e.id),c(e.stopLoss?.toString()||""),x(e.takeProfit?.toString()||"")},y=async r=>{try{let t=i?parseFloat(i):void 0,s=m?parseFloat(m):void 0;await o({id:r,stopLoss:t,takeProfit:s});let a=e.find(e=>e.id===r);h.oR.success("Position updated successfully",{description:`${a?.symbol} SL/TP updated`}),n(null),c(""),x("")}catch(e){console.error("Error updating position:",e),h.oR.error("Failed to update position. Please try again.")}};return r&&0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):t?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,s.jsx)(T.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("span",{children:t})]})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No open positions"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Size"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Entry Price"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Mark Price"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"PnL"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"SL/TP"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Liq. Price"}),(0,s.jsx)("th",{className:"text-center py-2 font-medium text-muted-foreground",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2 font-medium",children:e.symbol}),(0,s.jsx)("td",{className:`py-2 ${"LONG"===e.side?"text-emerald-500":"text-red-500"}`,children:e.side}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.size.toFixed(4)}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.entryPrice.toFixed(2)}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.markPrice.toFixed(2)}),(0,s.jsxs)("td",{className:"py-2 text-right font-medium",children:[e.leverage,"x"]}),(0,s.jsxs)("td",{className:`py-2 text-right ${e.pnl>=0?"text-emerald-500":"text-red-500"}`,children:[e.pnl>=0?"+":"",e.pnl.toFixed(2)," (",e.pnlPercent>=0?"+":"",e.pnlPercent.toFixed(2),"%)"]}),(0,s.jsx)("td",{className:"py-2 text-right",children:l===e.id?(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("input",{type:"text",value:i,onChange:e=>c(e.target.value),placeholder:"Stop Loss",className:"w-24 p-1 text-xs border border-border rounded"}),(0,s.jsx)("input",{type:"text",value:m,onChange:e=>x(e.target.value),placeholder:"Take Profit",className:"w-24 p-1 text-xs border border-border rounded"})]}):(0,s.jsxs)("div",{className:"text-xs",children:[(0,s.jsx)("div",{className:"text-red-500",children:e.stopLoss?`SL: ${e.stopLoss.toFixed(2)}`:"No SL"}),(0,s.jsx)("div",{className:"text-emerald-500",children:e.takeProfit?`TP: ${e.takeProfit.toFixed(2)}`:"No TP"})]})}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.liquidationPrice.toFixed(2)}),(0,s.jsx)("td",{className:"py-2 text-center",children:(0,s.jsxs)("div",{className:"flex justify-center space-x-1",children:[l===e.id?(0,s.jsx)("button",{onClick:()=>y(e.id),className:"p-1 rounded-md hover:bg-muted",title:"Save",children:(0,s.jsx)(A.A,{className:"h-3.5 w-3.5 text-emerald-500"})}):(0,s.jsx)("button",{onClick:()=>g(e),className:"p-1 rounded-md hover:bg-muted",title:"Edit SL/TP",children:(0,s.jsx)(C.A,{className:"h-3.5 w-3.5 text-muted-foreground"})}),(0,s.jsx)("button",{onClick:()=>f(e.id),className:`p-1 rounded-md hover:bg-muted ${p.has(e.id)?"opacity-50 cursor-not-allowed":""}`,title:"Close Position",disabled:p.has(e.id),children:p.has(e.id)?(0,s.jsx)("div",{className:"h-3.5 w-3.5 border border-muted-foreground border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(N.A,{className:"h-3.5 w-3.5 text-muted-foreground"})})]})})]},e.id))})]})})})}function U(){let{orders:e,isLoading:r,cancelOrder:t}=(0,u.fx)(),a=async r=>{try{let s=e.find(e=>e.id===r);await t(r),s&&h.oR.success("Order cancelled successfully",{description:`${s.symbol} ${s.side} order cancelled`})}catch(e){console.error("Error canceling order:",e),h.oR.error("Failed to cancel order. Please try again.")}},d=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return r?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No open orders"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Type"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Price"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Amount"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"SL/TP"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Time"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"font-medium",children:e.symbol})}),(0,s.jsx)("td",{className:"py-2",children:e.type}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"BUY"===e.side?"text-emerald-500":"text-red-500",children:e.side})}),(0,s.jsx)("td",{className:"py-2",children:e.price.toFixed(2)}),(0,s.jsx)("td",{className:"py-2",children:e.origQty.toFixed(4)}),(0,s.jsxs)("td",{className:"py-2 font-medium",children:[e.leverage,"x"]}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-red-500 mr-1",children:"SL:"}),(0,s.jsx)("span",{children:e.stopLoss?e.stopLoss.toFixed(2):"-"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-emerald-500 mr-1",children:"TP:"}),(0,s.jsx)("span",{children:e.takeProfit?e.takeProfit.toFixed(2):"-"})]})]})}),(0,s.jsx)("td",{className:"py-2",children:d(e.timestamp)}),(0,s.jsx)("td",{className:"py-2 text-right",children:(0,s.jsx)("button",{onClick:()=>a(e.id),className:"p-1 rounded-md hover:bg-muted",title:"Cancel Order",children:(0,s.jsx)(N.A,{className:"h-3.5 w-3.5"})})})]},e.id))})]})})})}function D(){let{trades:e,isLoading:r}=(0,u.fx)(),t=e=>new Date(e).toLocaleString([],{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return r?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No trade history"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Time"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Price"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Quantity"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Fee"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Realized PnL"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2",children:t(e.timestamp)}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"font-medium",children:e.symbol})}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"BUY"===e.side?"text-emerald-500":"text-red-500",children:e.side})}),(0,s.jsx)("td",{className:"py-2",children:e.price.toFixed(2)}),(0,s.jsx)("td",{className:"py-2",children:e.quantity.toFixed(4)}),(0,s.jsxs)("td",{className:"py-2 font-medium",children:[e.leverage,"x"]}),(0,s.jsxs)("td",{className:"py-2",children:[e.commission.toFixed(2)," USD"]}),(0,s.jsx)("td",{className:"py-2 text-right",children:(0,s.jsxs)("span",{className:e.realizedPnl>0?"text-emerald-500":e.realizedPnl<0?"text-red-500":"",children:[e.realizedPnl>0?"+":"",e.realizedPnl.toFixed(2)," USD"]})})]},e.id))})]})})})}function L(){let{updateMarketData:e,positions:r,orders:t,trades:d,getTotalPnL:o}=(0,u.fx)(),[l,n]=(0,a.useState)(!1),[c,m]=(0,a.useState)("BTCUSDT"),[x,h]=(0,a.useState)("0"),[b,f]=(0,a.useState)("positions"),[g,y]=(0,a.useState)(!1),[N,j]=(0,a.useState)("disconnected"),[v,w]=(0,a.useState)(!1),[S,k]=(0,a.useState)(10),[T,A]=(0,a.useState)(!0),C=(0,a.useRef)(null),L=(0,a.useRef)(null),R=(0,a.useRef)(0),$=()=>r.reduce((e,r)=>e+r.pnl,0),E=()=>d.reduce((e,r)=>e+r.realizedPnl,0),M=e=>{let r=e>0?"+":"";return`${r}${e.toFixed(2)} USD`},I=(0,a.useCallback)(async()=>{try{let r=new AbortController,t=setTimeout(()=>r.abort(),5e3),s=await fetch(`https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=${c}`,{signal:r.signal});if(clearTimeout(t),!s.ok)throw Error(`HTTP error! status: ${s.status}`);let a=await s.json();if(a&&a.lastPrice){let r=Number.parseFloat(a.lastPrice);h(r.toFixed(r>=1e3?0:r>=1?2:4)),e(c,{price:r,priceChange:Number.parseFloat(a.priceChange||"0"),priceChangePercent:Number.parseFloat(a.priceChangePercent||"0"),volume:Number.parseFloat(a.volume||"0")})}}catch(e){"AbortError"===e.name?console.warn("Price data fetch timeout for",c):console.warn("Error fetching price data via REST:",e.message)}},[c,e]),B=(0,a.useCallback)(()=>{if(L.current&&(clearTimeout(L.current),L.current=null),C.current&&C.current.readyState===WebSocket.OPEN)try{C.current.close()}catch(e){console.error("Error closing existing WebSocket:",e)}try{j("connecting");let r=new WebSocket("wss://fstream.binance.com/ws");C.current=r;let t=setTimeout(()=>{r.readyState===WebSocket.CONNECTING&&(console.warn("WebSocket connection timeout, falling back to REST API"),r.close(),j("error"),I())},1e4);r.addEventListener("open",()=>{clearTimeout(t)}),r.onopen=()=>{console.log("WebSocket connected"),j("connected"),R.current=0;try{let e=JSON.stringify({method:"SUBSCRIBE",params:[`${c.toLowerCase()}@ticker`],id:1});r.send(e)}catch(e){console.error("Error sending subscription message:",e)}},r.onmessage=r=>{try{let t=JSON.parse(r.data);if("24hrTicker"===t.e){let r=Number.parseFloat(t.c),s=r.toFixed(r>=1e3?0:r>=1?2:4);h(s),e(c,{price:r,priceChange:Number.parseFloat(t.P),priceChangePercent:Number.parseFloat(t.P),volume:Number.parseFloat(t.v)})}}catch(e){console.error("Error processing WebSocket message:",e)}},r.onerror=e=>{console.warn("WebSocket connection failed, using REST API fallback:",e),j("error"),I();let r=setInterval(I,5e3);C.current||(C.current={intervalId:r})},r.onclose=e=>{if(console.log("WebSocket connection closed:",e),j("disconnected"),R.current<5)R.current+=1,console.log(`Attempting to reconnect (${R.current}/5)...`),L.current=setTimeout(()=>{B()},3e3);else{console.log("Max reconnect attempts reached. Using REST API fallback.");let e=setInterval(I,5e3);return()=>clearInterval(e)}}}catch(e){console.warn("Failed to initialize WebSocket, using REST API fallback:",e),j("error"),I()}},[c,I]),O=async e=>{console.log("Order submitted via trade page:",e)};return(0,s.jsxs)("div",{className:"h-full bg-background",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 xl:grid-cols-5 gap-3 h-full p-3",children:[(0,s.jsxs)("div",{className:"lg:col-span-3 xl:col-span-4 space-y-3",children:[(0,s.jsx)("div",{children:(0,s.jsx)(P,{symbol:c,onSymbolChange:e=>{if(m(e),I(),C.current&&C.current.readyState===WebSocket.OPEN)try{let r=JSON.stringify({method:"UNSUBSCRIBE",params:[`${c.toLowerCase()}@ticker`],id:2});C.current.send(r);let t=JSON.stringify({method:"SUBSCRIBE",params:[`${e.toLowerCase()}@ticker`],id:1});C.current.send(t)}catch(e){console.error("Error updating WebSocket subscription:",e),B()}else B()},leverage:S,onLeverageChange:k,isCrossMargin:T,onCrossMarginChange:A,connectionStatus:N,onRetryConnection:B})}),(0,s.jsx)("div",{className:"h-[400px] sm:h-[500px] lg:h-[600px]",children:(0,s.jsx)(i,{symbol:c,interval:"15"})}),(0,s.jsx)("div",{className:"mb-2 sm:mb-3",children:(0,s.jsxs)("div",{className:"flex border-b border-border overflow-x-auto",children:[(0,s.jsx)("button",{className:`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${"positions"===b?"border-b-2 border-primary text-primary":"text-muted-foreground"}`,onClick:()=>f("positions"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{children:"Positions"}),r.length>0&&(0,s.jsx)("span",{className:`text-xs ${$()>=0?"text-emerald-500":"text-red-500"}`,children:M($())})]})}),(0,s.jsx)("button",{className:`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${"orders"===b?"border-b-2 border-primary text-primary":"text-muted-foreground"}`,onClick:()=>f("orders"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Open Orders"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Orders"}),t.length>0&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[t.length," order",1!==t.length?"s":""]})]})}),(0,s.jsx)("button",{className:`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${"history"===b?"border-b-2 border-primary text-primary":"text-muted-foreground"}`,onClick:()=>f("history"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Trade History"}),(0,s.jsx)("span",{className:"sm:hidden",children:"History"}),d.length>0&&(0,s.jsx)("span",{className:`text-xs ${E()>=0?"text-emerald-500":"text-red-500"}`,children:M(E())})]})})]})}),(0,s.jsxs)("div",{children:["positions"===b&&(0,s.jsx)(F,{}),"orders"===b&&(0,s.jsx)(U,{}),"history"===b&&(0,s.jsx)(D,{})]})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:col-span-1 xl:col-span-1",children:(0,s.jsx)("div",{className:"h-[calc(100vh-120px)] w-full max-w-[350px] lg:max-w-none mx-auto sticky top-4",children:(0,s.jsx)(p,{symbol:c,lastPrice:x,leverage:S,onOrderSubmit:O})})})]}),v&&(0,s.jsx)("div",{className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>w(!1),children:(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-background rounded-t-2xl border-t border-border max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-lg font-bold",children:"Place Order"}),(0,s.jsx)("button",{onClick:()=>w(!1),className:"p-2 rounded-full hover:bg-muted",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)(p,{symbol:c,lastPrice:x,leverage:S,onOrderSubmit:e=>{O(e),w(!1)}})]})})}),(0,s.jsxs)("button",{onClick:()=>w(!0),className:"lg:hidden fixed bottom-6 right-6 z-40 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white rounded-full p-4 shadow-xl shadow-emerald-600/30 transition-all duration-300 hover:scale-110 active:scale-95 group",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M12 4v16m8-8H4"})}),(0,s.jsx)("span",{className:"text-sm font-bold tracking-wide",children:"TRADE"})]}),(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-emerald-600 animate-ping opacity-20"}),(0,s.jsxs)("div",{className:"absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:["Place Order",(0,s.jsx)("div",{className:"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"})]})]})]})}function R(){return(0,s.jsx)(L,{})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96748:(e,r,t)=>{Promise.resolve().then(t.bind(t,89712))},99270:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[128,658,817,452,895,887],()=>t(55090));module.exports=s})();
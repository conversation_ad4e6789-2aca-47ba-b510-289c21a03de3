"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{17069:(t,e,i)=>{i.d(e,{rM:()=>p,fx:()=>y});var s=i(95155),a=i(12115),r=i(81115),o=i(98915),n=i(50475),l=i(91317),c=i(27759);class u{subscribe(t){return this.subscribers.push(t),t(this.state),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(){this.subscribers.forEach(t=>t(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(t){this.userId=t,this.state.isLoading=!0,this.notifySubscribers();try{await this.initializeAccountInfo(),this.setupRealtimeListeners(),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized for user:",t)}catch(t){console.error("Error initializing realtime trading service:",t),this.state.isLoading=!1,this.state.error="Failed to initialize trading service",this.notifySubscribers()}}async initializeAccountInfo(){if(!this.userId)return;let t=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:t,totalUnrealizedProfit:0,totalMarginBalance:t,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:t,maxWithdrawAmount:t,updateTime:Date.now()}}setupRealtimeListeners(){if(!this.userId)return;let t=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions")),e=(0,r.Zy)(t,t=>{let e=t.val();this.state.positions=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),i=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders")),s=(0,r.Zy)(i,t=>{let e=t.val();this.state.orders=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}),a=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/trades")),n=(0,r.Zy)(a,t=>{let e=t.val();this.state.trades=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}).sort((t,e)=>e.timestamp-t.timestamp):[],this.notifySubscribers()});this.unsubscribeFunctions=[()=>(0,r.AU)(t,"value",e),()=>(0,r.AU)(i,"value",s),()=>(0,r.AU)(a,"value",n)]}updateAccountInfo(){if(!this.state.accountInfo)return;let t=n.A.getUserBalance(),e=this.state.positions.reduce((t,e)=>t+(e.pnl||0),0),i=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),s=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.quantity*s/(e.leverage||10)},0),a=i+s,r=Math.max(0,t-a);this.state.accountInfo.totalWalletBalance=t,this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=i,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=t+e,this.state.accountInfo.availableBalance=r,this.state.accountInfo.maxWithdrawAmount=r,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:t,totalMargin:i,totalOrderMargin:s,totalUsedMargin:a,availableBalance:r,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(t=>"NEW"===t.status).length})}updateMarketData(t,e){this.state.marketData[t]={...this.state.marketData[t],...e},e.price&&(this.state.positions=this.state.positions.map(i=>i.symbol===t?this.updatePositionPnL(i,e.price):i),this.updateAccountInfo(),this.notifySubscribersDebounced())}calculatePnL(t,e){let i=("LONG"===t.side?e-t.entryPrice:t.entryPrice-e)*t.size,s=t.margin>0?i/t.margin*100:0;return{pnl:Number(i.toFixed(2)),pnlPercent:Number(s.toFixed(2))}}updatePositionPnL(t,e){let{pnl:i,pnlPercent:s}=this.calculatePnL(t,e);return{...t,markPrice:e,pnl:i,pnlPercent:s}}calculateLiquidationPrice(t,e,i){let s=.995-1/i;return"LONG"===e?t*s:t*(2-s)}async placeOrder(t){var e;if(!this.userId){let t=n.A.getFirebaseUser(),e=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:t?{uid:t.uid,email:t.email}:null,userServiceUser:e?{id:e.id,email:e.email}:null}),t)console.log("Using Firebase user ID as fallback:",t.uid),this.userId=t.uid,await this.initializeForUser(t.uid);else if(e&&e.id)console.log("Using user service ID as fallback:",e.id),this.userId=e.id,await this.initializeForUser(e.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let i=Date.now();if(i-this.lastOrderTime<this.ORDER_COOLDOWN){let t=this.ORDER_COOLDOWN-(i-this.lastOrderTime);throw Error("Please wait ".concat(Math.ceil(t/1e3)," second(s) before placing another order"))}let s=(null===(e=this.state.marketData[t.symbol])||void 0===e?void 0:e.price)||t.price||0;if(s<=0)throw Error("Invalid market price. Please try again.");let a=t.quantity*s,l=a/(t.leverage||10),u=.001*a,d=n.A.getUserBalance(),h=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),b=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.quantity*s/(e.leverage||10)},0),m=h+b,p=d-m,y=l+u;if(console.log("Balance Validation:",{userBalance:d,currentMargin:h,pendingOrderMargin:b,totalUsedMargin:m,availableBalance:p,requiredMargin:l,commission:u,totalRequired:y,orderValue:a,leverage:t.leverage||10}),y>p)throw Error("Insufficient balance. Required: ".concat(y.toFixed(2)," USDT, Available: ").concat(p.toFixed(2)," USDT"));if(p<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let f="ord_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),I={id:f,symbol:t.symbol,side:t.side,type:t.type,quantity:t.quantity,price:t.price||s,status:"NEW",timestamp:Date.now(),executedQty:0,leverage:t.leverage||10};this.state.orders.push(I),this.updateAccountInfo(),this.notifySubscribers(),this.lastOrderTime=i;try{let e=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(f));return await (0,r.hZ)(e,{...I,createdAt:(0,r.O5)()}),"MARKET"===t.type&&await this.executeOrder(f,I,s),c.l.createTradeNotification(this.userId,"order_placed",{symbol:t.symbol,side:t.side,type:t.type,price:t.price||s,quantity:t.quantity}),f}catch(t){throw this.state.orders=this.state.orders.filter(t=>t.id!==f),this.updateAccountInfo(),this.notifySubscribers(),t}}async executeOrder(t,e,i){if(!this.userId)return;let s=e.quantity*i*.001,a=e.quantity*i/e.leverage,u=n.A.getUserBalance(),d=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),h=u-d,b=a+s;if(console.log("Execution Balance Check:",{userBalance:u,currentMargin:d,availableBalance:h,margin:a,commission:s,totalRequired:b,orderId:t}),b>h)throw this.state.orders=this.state.orders.filter(e=>e.id!==t),this.updateAccountInfo(),this.notifySubscribers(),Error("Execution failed: Insufficient balance. Required: ".concat(b.toFixed(2)," USDT, Available: ").concat(h.toFixed(2)," USDT"));let m="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),p={id:m,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:i,markPrice:i,size:e.quantity,margin:a,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(i,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:t};this.state.positions.push(p);let y=this.state.orders.findIndex(e=>e.id===t);-1!==y&&(this.state.orders[y].status="FILLED",this.state.orders[y].executedQty=e.quantity),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:p.side,size:e.quantity,entryPrice:i});try{let e=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(m)),i=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/orders/").concat(t));await Promise.all([(0,r.hZ)(e,{...p,createdAt:(0,r.O5)()}),(0,r.hZ)(i,{...this.state.orders[y],updatedAt:(0,r.O5)()})]);let a=n.A.getUserBalance();await n.A.updateBalance(a-s,"commission","Trading commission: ".concat(s.toFixed(2)," USDT")),l.b.addPosition(this.userId,p).catch(console.error)}catch(t){console.error("Error saving position to Firebase:",t)}}async closePosition(t){var e;if(!this.userId)return;let i=this.state.positions.findIndex(e=>e.id===t);if(-1===i)return;let s=this.state.positions[i],a=(null===(e=this.state.marketData[s.symbol])||void 0===e?void 0:e.price)||s.markPrice,u=s.size*a*.001;this.state.positions.splice(i,1),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_closed",{symbol:s.symbol,side:s.side,pnl:s.pnl,closePrice:a});try{let e="trade_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),i={id:e,symbol:s.symbol,side:"LONG"===s.side?"SELL":"BUY",price:a,quantity:s.size,commission:u,realizedPnl:s.pnl,timestamp:Date.now(),leverage:s.leverage,orderId:s.orderId||"",positionId:t};this.state.trades.unshift(i),this.notifySubscribers();let c=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/positions/").concat(t)),d=(0,r.KR)(o.Ye,"users/".concat(this.userId,"/trades/").concat(e));await Promise.all([(0,r.TF)(c),(0,r.hZ)(d,{...i,createdAt:(0,r.O5)()})]);let h=n.A.getUserBalance();await n.A.updateBalance(h+s.pnl-u,s.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(s.pnl>0?"+":"").concat(s.pnl.toFixed(2)," USDT")),l.b.addTrade(this.userId,i).catch(console.error)}catch(t){throw console.error("Error closing position in Firebase:",t),this.state.positions.push(s),this.updateAccountInfo(),this.notifySubscribers(),t}}getState(){return{...this.state}}getMarketData(t){return this.state.marketData[t]||null}cleanup(){this.unsubscribeFunctions.forEach(t=>t()),this.unsubscribeFunctions=[],this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.userId=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.notifySubscribers()}constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,n.A.subscribe(t=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!t,userId:null==t?void 0:t.id,userStructure:t?Object.keys(t):null}),t&&t.id?this.initializeForUser(t.id):this.cleanup()})}}let d=new u;class h{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(t=>{this.simulators.set(t.symbol,{symbol:t.symbol,basePrice:t.basePrice,volatility:t.volatility,trend:(Math.random()-.5)*.001,lastPrice:t.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},2e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(t=>{let e=Date.now(),i=(e-t.lastUpdate)/1e3,s=(Math.random()-.5)*t.volatility*i,a=t.trend*i,r=t.lastPrice*(1+(s+a)),o=r-t.lastPrice,n=o/t.lastPrice*100;.01>Math.random()&&(t.trend=(Math.random()-.5)*.001),t.lastPrice=r,t.lastUpdate=e;let l={symbol:t.symbol,price:r,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:e};this.notifySubscribers(t.symbol,l)})}subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(t,e){this.subscribers.forEach(i=>i(t,e))}getCurrentPrice(t){let e=this.simulators.get(t);return e?e.lastPrice:null}addSymbol(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(t)||this.simulators.set(t,{symbol:t,basePrice:e,volatility:i,trend:(Math.random()-.5)*.001,lastPrice:e,lastUpdate:Date.now()})}removeSymbol(t){this.simulators.delete(t)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let b=new h,m=(0,a.createContext)(void 0);function p(t){let{children:e}=t,[i,r]=(0,a.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,a.useEffect)(()=>d.subscribe(t=>{r(t)}),[]),(0,a.useEffect)(()=>{let t=b.subscribe((t,e)=>{d.updateMarketData(t,e)});return b.start(),()=>{t(),b.stop()}},[]);let o=(0,a.useCallback)(async t=>{try{return await d.placeOrder(t)}catch(t){throw console.error("Failed to place order:",t),t}},[]),n=(0,a.useCallback)(async t=>{try{return console.log("Cancel order not implemented yet:",t),!0}catch(t){throw console.error("Failed to cancel order:",t),t}},[]),l=(0,a.useCallback)(async t=>{try{return await d.closePosition(t),!0}catch(t){throw console.error("Failed to close position:",t),t}},[]),c=(0,a.useCallback)(async t=>{try{return console.log("Position update not implemented in Firebase service yet:",t),!0}catch(t){throw console.error("Failed to update position:",t),t}},[]),u=(0,a.useCallback)((t,e)=>{d.updateMarketData(t,e)},[]),h=(0,a.useCallback)(t=>{console.log("Account info update not needed with Realtime service:",t)},[]),p=(0,a.useCallback)(()=>{r(t=>({...t,error:null}))},[]),y=(0,a.useCallback)(t=>d.getMarketData(t),[]),f=(0,a.useCallback)(t=>i.positions.find(e=>e.symbol===t)||null,[i.positions]),I=(0,a.useCallback)(()=>i.accountInfo,[i.accountInfo]),g=(0,a.useCallback)(()=>i.positions.reduce((t,e)=>t+e.pnl,0),[i.positions]),v=(0,a.useCallback)(()=>i.positions.reduce((t,e)=>t+e.margin,0),[i.positions]),D=(0,a.useCallback)(()=>{var t;return(null===(t=i.accountInfo)||void 0===t?void 0:t.availableBalance)||0},[i.accountInfo]),P={positions:i.positions,orders:i.orders,trades:i.trades,marketData:i.marketData,accountInfo:i.accountInfo,isLoading:i.isLoading,error:i.error,state:i,placeOrder:o,cancelOrder:n,closePosition:l,updatePosition:c,updateMarketData:u,updateAccountInfo:h,clearError:p,getMarketData:y,getPositionBySymbol:f,getAccountInfo:I,getTotalPnL:g,getTotalMargin:v,getAvailableBalance:D};return(0,s.jsx)(m.Provider,{value:P,children:e})}function y(){let t=(0,a.useContext)(m);if(void 0===t)throw Error("useTrading must be used within a TradingProvider");return t}}}]);
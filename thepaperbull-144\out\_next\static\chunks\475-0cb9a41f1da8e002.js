"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[475],{27759:(e,r,t)=>{t.d(r,{A:()=>o,l:()=>n});var s=t(35317),i=t(98915);class a{initialize(e){this.currentUser=e,this.unsubscribeFirestore&&(this.unsubscribeFirestore(),this.unsubscribeFirestore=null),e?this.subscribeToUserNotifications(e.uid):(this.notifications=[],this.notifySubscribers())}subscribeToUserNotifications(e){let r=(0,s.rJ)(i.db,"notifications"),t=(0,s.P)(r,(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));this.unsubscribeFirestore=(0,s.aQ)(t,e=>{let r=e.docs.map(e=>({id:e.id,...e.data()})),t=this.notifications.filter(e=>e.id.startsWith("temp_"));this.notifications=[...t,...r],this.notifySubscribers()},e=>{console.error("Error listening to notifications:",e)})}subscribe(e){return this.subscribers.push(e),e(this.notifications),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.notifications))}getNotifications(){return this.notifications}getUnreadCount(){return this.notifications.filter(e=>!e.read).length}async markAsRead(e){if(this.currentUser)try{let r=(0,s.H9)(i.db,"notifications",e);await (0,s.mZ)(r,{read:!0})}catch(e){console.error("Error marking notification as read:",e)}}async markAllAsRead(){if(this.currentUser)try{let e=this.notifications.filter(e=>!e.read).map(e=>this.markAsRead(e.id));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}}async createNotification(e,r,t){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"info",n=arguments.length>4?arguments[4]:void 0;try{let o={id:"temp_".concat(Date.now()),userId:e,title:r,message:t,type:a,read:!1,createdAt:new Date,data:n||null};this.notifications.unshift(o),this.notifySubscribers();let c=(0,s.rJ)(i.db,"notifications"),d=await (0,s.gS)(c,{userId:e,title:r,message:t,type:a,read:!1,createdAt:(0,s.O5)(),data:n||null}),u=this.notifications.findIndex(e=>e.id===o.id);-1!==u&&(this.notifications[u].id=d.id)}catch(e){console.error("Error creating notification:",e)}}async createTradeNotification(e,r,t){let s="",i="";switch(r){case"order_filled":s="Order Filled",i="Your ".concat(t.side," order for ").concat(t.symbol," has been filled at $").concat(t.price);break;case"position_opened":s="Position Opened",i="New ".concat(t.side," position opened for ").concat(t.symbol," - Size: ").concat(t.size);break;case"position_closed":s="Position Closed",i="".concat(t.symbol," position closed - P&L: ").concat(t.pnl>0?"+":"","$").concat(t.pnl.toFixed(2));break;case"stop_loss":s="Stop Loss Triggered",i="Stop loss triggered for ".concat(t.symbol," at $").concat(t.price);break;case"take_profit":s="Take Profit Hit",i="Take profit reached for ".concat(t.symbol," at $").concat(t.price)}await this.createNotification(e,s,i,"trade",t)}async createWelcomeNotification(e){await this.createNotification(e,"Welcome to ThePaperBull!","Start your paper trading journey with $10,000 virtual balance. Practice trading without risk!","success")}destroy(){this.unsubscribeFirestore&&this.unsubscribeFirestore(),this.subscribers=[],this.notifications=[]}constructor(){this.subscribers=[],this.unsubscribeFirestore=null,this.currentUser=null,this.notifications=[]}}let n=new a,o=n},50475:(e,r,t)=>{t.d(r,{A:()=>u});var s=t(91317),i=t(98915),a=t(16203),n=t(27759),o=t(60199);function c(e){var r,t,s,i,a,n,o,c;return{id:e.id,email:e.email,name:e.name,createdAt:(null===(t=e.createdAt)||void 0===t?void 0:null===(r=t.toMillis)||void 0===r?void 0:r.call(t))||Date.now(),subscription:{type:e.subscription.type,status:e.subscription.status,startDate:(null===(i=e.subscription.startDate)||void 0===i?void 0:null===(s=i.toMillis)||void 0===s?void 0:s.call(i))||Date.now(),endDate:null===(n=e.subscription.endDate)||void 0===n?void 0:null===(a=n.toMillis)||void 0===a?void 0:a.call(n),features:e.subscription.features},balance:{current:e.balance.current,maximum:e.balance.maximum,currency:e.balance.currency,lastUpdated:(null===(c=e.balance.lastUpdated)||void 0===c?void 0:null===(o=c.toMillis)||void 0===o?void 0:o.call(c))||Date.now(),transactions:[]},settings:e.settings}}class d{initializeAuth(){this.unsubscribeAuth=(0,a.hg)(i.j2,async e=>{this.firebaseUser=e,e?await this.loadUserProfile(e.uid):(this.currentUser=null,this.transactions=[],this.unsubscribeProfile&&(this.unsubscribeProfile(),this.unsubscribeProfile=null),this.notifySubscribers())})}async loadUserProfile(e){try{let r=await s.b.getUserProfile(e);!r&&this.firebaseUser&&(r=await s.b.createUserProfile(e,this.firebaseUser.email||"",this.firebaseUser.displayName||"User"))&&await n.A.createWelcomeNotification(e),r&&(this.currentUser=c(r),this.transactions=await s.b.getUserTransactions(e),this.currentUser.balance.transactions=this.transactions.map(e=>{var r,t;return{id:e.id,type:e.type,amount:e.amount,balance_before:e.balance_before,balance_after:e.balance_after,description:e.description,timestamp:(null===(t=e.timestamp)||void 0===t?void 0:null===(r=t.toMillis)||void 0===r?void 0:r.call(t))||Date.now()}}),this.unsubscribeProfile&&this.unsubscribeProfile(),this.unsubscribeProfile=s.b.subscribeToUserProfile(e,e=>{e&&(this.currentUser=c(e),this.notifySubscribers())}),this.notifySubscribers())}catch(e){console.error("Error loading user profile:",e),"permission-denied"===e.code?(console.error("Permission denied - Firestore security rules may need to be updated"),this.firebaseUser&&(this.currentUser={id:this.firebaseUser.uid,email:this.firebaseUser.email||"",name:this.firebaseUser.displayName||"User",avatar:"bull-trader",balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:Date.now(),transactions:[]},subscription:{type:"free",status:"active"},settings:{},createdAt:Date.now(),lastLoginAt:Date.now()},this.notifySubscribers())):(this.currentUser=null,this.notifySubscribers())}}notifySubscribers(){this.subscribers.forEach(e=>e(this.currentUser))}getUser(){return this.currentUser}getUserBalance(){var e,r;return(null===(r=this.currentUser)||void 0===r?void 0:null===(e=r.balance)||void 0===e?void 0:e.current)||1e4}getMaxBalance(){var e;return(null===(e=this.currentUser)||void 0===e?void 0:e.balance.maximum)||o.sC}getAvailableFunds(){return this.currentUser?this.currentUser.balance.maximum-this.currentUser.balance.current:0}canAddFunds(e){return this.currentUser?e<=0?{canAdd:!1,reason:"Amount must be positive"}:this.currentUser.balance.current+e>this.currentUser.balance.maximum?{canAdd:!1,reason:"Would exceed maximum balance of ".concat(this.currentUser.balance.maximum.toLocaleString()," USDT")}:{canAdd:!0}:{canAdd:!1,reason:"User not found"}}async addFunds(e){if(!this.currentUser||!this.firebaseUser)return{success:!1,new_balance:0,requires_subscription:!1,message:"User not found"};let r=this.canAddFunds(e.amount);if(!r.canAdd){let t=this.currentUser.balance.current+e.amount<=o.ND.premium.maxBalance;return{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:t,message:r.reason||"Cannot add funds"}}try{let r=this.currentUser.balance.current,t=r+e.amount;return await s.b.updateUserBalance(this.firebaseUser.uid,t),await s.b.addTransaction(this.firebaseUser.uid,{type:"deposit",amount:e.amount,balance_before:r,balance_after:t,description:"subscription_upgrade"===e.method?"Funds added via subscription":"Virtual funds added"}),{success:!0,new_balance:t,requires_subscription:!1,message:"Successfully added ".concat(e.amount.toLocaleString()," USDT to your account")}}catch(e){return console.error("Error adding funds:",e),{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:!1,message:"Failed to add funds. Please try again."}}}async upgradeSubscription(e){if(!this.currentUser||!this.firebaseUser)return!1;try{let r=o.ND[e];return await s.b.updateUserProfile(this.firebaseUser.uid,{subscription:{type:e,status:"active",startDate:new Date,endDate:new Date(Date.now()+31536e6),features:r.features},balance:{...this.currentUser.balance,maximum:r.maxBalance}}),!0}catch(e){return console.error("Error upgrading subscription:",e),!1}}async updateBalance(e,r,t){if(this.currentUser&&this.firebaseUser)try{let i=this.currentUser.balance.current;await s.b.updateUserBalance(this.firebaseUser.uid,e),await s.b.addTransaction(this.firebaseUser.uid,{type:r,amount:e-i,balance_before:i,balance_after:e,description:t})}catch(e){console.error("Error updating balance:",e)}}subscribe(e){return this.subscribers.push(e),e(this.currentUser),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}getFirebaseUser(){return this.firebaseUser}destroy(){this.unsubscribeAuth&&this.unsubscribeAuth(),this.unsubscribeProfile&&this.unsubscribeProfile()}constructor(){this.currentUser=null,this.firebaseUser=null,this.subscribers=[],this.unsubscribeAuth=null,this.unsubscribeProfile=null,this.transactions=[],this.initializeAuth()}}let u=new d},60199:(e,r,t)=>{t.d(r,{ND:()=>s,sC:()=>i});let s={free:{type:"free",name:"Free",maxBalance:5e4,price:0,features:{maxBalance:5e4,advancedAnalytics:!1,prioritySupport:!1,customIndicators:!1,apiAccess:!1}},premium:{type:"premium",name:"Premium",maxBalance:5e5,price:29.99,features:{maxBalance:5e5,advancedAnalytics:!0,prioritySupport:!1,customIndicators:!0,apiAccess:!1}},pro:{type:"pro",name:"Pro",maxBalance:1e6,price:99.99,features:{maxBalance:1e6,advancedAnalytics:!0,prioritySupport:!0,customIndicators:!0,apiAccess:!0}}},i=5e4},91317:(e,r,t)=>{t.d(r,{b:()=>n});var s=t(35317),i=t(98915);class a{async createUserProfile(e,r,t){let a=(0,s.O5)(),n={email:r,name:t,avatar:"bull-trader",createdAt:a,subscription:{type:"free",status:"active",startDate:a,features:["basic_trading","paper_trading","basic_charts"]},balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:a},settings:{theme:"system",notifications:{email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1},trading:{default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0}}};return await (0,s.BN)((0,s.H9)(i.db,"users",e),n),await this.addTransaction(e,{type:"deposit",amount:1e4,balance_before:0,balance_after:1e4,description:"Initial balance"}),{id:e,...n}}async getUserProfile(e){try{let r=await (0,s.x7)((0,s.H9)(i.db,"users",e));if(r.exists())return{id:e,...r.data()};return null}catch(e){return console.error("Error getting user profile:",e),null}}async updateUserProfile(e,r){try{let t={...r};r.balance&&(t["balance.lastUpdated"]=(0,s.O5)()),await (0,s.mZ)((0,s.H9)(i.db,"users",e),t)}catch(e){throw console.error("Error updating user profile:",e),e}}async updateUserBalance(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"users",e),{"balance.current":r,"balance.lastUpdated":(0,s.O5)()})}catch(e){throw console.error("Error updating user balance:",e),e}}async addTransaction(e,r){try{let t={...r,userId:e,timestamp:(0,s.O5)()};return(await (0,s.gS)((0,s.rJ)(i.db,"transactions"),t)).id}catch(e){throw console.error("Error adding transaction:",e),e}}async getUserTransactions(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{let t=(0,s.P)((0,s.rJ)(i.db,"transactions"),(0,s._M)("userId","==",e),(0,s.My)("timestamp","desc"));return new Promise((e,i)=>{(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));e(s.slice(0,r))},i)})}catch(e){return console.error("Error getting user transactions:",e),[]}}async addPosition(e,r){try{console.log("Adding position to Firestore:",{userId:e,position:r});let t=(0,s.O5)(),a={...r,userId:e,createdAt:t,updatedAt:t},n=await (0,s.gS)((0,s.rJ)(i.db,"positions"),a);return console.log("Position added successfully with ID:",n.id),n.id}catch(e){if(console.error("Error adding position to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error("Failed to create position: ".concat(e.message||"Unknown error"))}}async updatePosition(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"positions",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating position:",e),e}}async deletePosition(e){try{await (0,s.kd)((0,s.H9)(i.db,"positions",e))}catch(e){throw console.error("Error deleting position:",e),e}}async getUserPositions(e){try{let r=(0,s.P)((0,s.rJ)(i.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user positions:",e),[]}}async addOrder(e,r){try{console.log("Adding order to Firestore:",{userId:e,order:r});let t=(0,s.O5)(),a={...r,userId:e,createdAt:t,updatedAt:t},n=await (0,s.gS)((0,s.rJ)(i.db,"orders"),a);return console.log("Order added successfully with ID:",n.id),n.id}catch(e){if(console.error("Error adding order to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");if("failed-precondition"===e.code)throw Error("Database operation failed. Please check your connection and try again.");throw Error("Failed to place order: ".concat(e.message||"Unknown error"))}}async updateOrder(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"orders",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating order:",e),e}}async deleteOrder(e){try{await (0,s.kd)((0,s.H9)(i.db,"orders",e))}catch(e){throw console.error("Error deleting order:",e),e}}async getUserOrders(e){try{let r=(0,s.P)((0,s.rJ)(i.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user orders:",e),[]}}async addTrade(e,r){try{console.log("Adding trade to Firestore:",{userId:e,trade:r});let t={...r,userId:e,createdAt:(0,s.O5)()},a=await (0,s.gS)((0,s.rJ)(i.db,"trades"),t);return console.log("Trade added successfully with ID:",a.id),a.id}catch(e){if(console.error("Error adding trade to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error("Failed to record trade: ".concat(e.message||"Unknown error"))}}async getUserTrades(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;try{let t=(0,s.P)((0,s.rJ)(i.db,"trades"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,i)=>{let a=(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));a(),e(s.slice(0,r))},e=>{i(e)})})}catch(e){return console.error("Error getting user trades:",e),[]}}subscribeToUserProfile(e,r){return(0,s.aQ)((0,s.H9)(i.db,"users",e),t=>{t.exists()?r({id:e,...t.data()}):r(null)})}subscribeToUserPositions(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}subscribeToUserOrders(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}subscribeToUserTrades(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"trades"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}}let n=new a},98915:(e,r,t)=>{t.d(r,{Ye:()=>l,db:()=>u,j2:()=>d});var s=t(23915),i=t(16203),a=t(35317),n=t(81115),o=t(86864);let c=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s",authDomain:"thepaperbull-144.firebaseapp.com",databaseURL:"https://thepaperbull-144-default-rtdb.firebaseio.com/",projectId:"thepaperbull-144",storageBucket:"thepaperbull-144.firebasestorage.app",messagingSenderId:"540770032311",appId:"1:540770032311:web:54b0d4ec1715779408cb32",measurementId:"G-5KTY505WKQ"}):(0,s.Dk)()[0],d=(0,i.xI)(c),u=(0,a.aU)(c),l=(0,n.C3)(c);(0,o.P5)(c)}}]);
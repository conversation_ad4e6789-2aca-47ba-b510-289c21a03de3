"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import type {
  TradingState,
  OrderRequest,
  PositionUpdate,
  Position,
  Order,
  Trade,
  MarketData,
  AccountInfo
} from '../types/trading'
import realtimeTradingService from '../lib/realtime-trading-service'
import marketDataSimulator from '../lib/market-data-simulator'

interface TradingContextType {
  // State
  positions: Position[]
  orders: Order[]
  trades: Trade[]
  marketData: Record<string, MarketData>
  accountInfo: AccountInfo | null
  isLoading: boolean
  error: string | null
  state: TradingState

  // Actions
  placeOrder: (order: OrderRequest) => Promise<string>
  cancelOrder: (orderId: string) => Promise<boolean>
  closePosition: (positionId: string) => Promise<boolean>
  updatePosition: (update: PositionUpdate) => Promise<boolean>
  updateMarketData: (symbol: string, data: Partial<MarketData>) => void
  updateAccountInfo: (accountInfo: Partial<AccountInfo>) => void
  clearError: () => void

  // Utilities
  getMarketData: (symbol: string) => MarketData | null
  getPositionBySymbol: (symbol: string) => Position | null
  getAccountInfo: () => AccountInfo | null
  getTotalPnL: () => number
  getTotalMargin: () => number
  getAvailableBalance: () => number
}

const TradingContext = createContext<TradingContextType | undefined>(undefined)

export function TradingProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<TradingState>({
    positions: [],
    orders: [],
    trades: [],
    marketData: {},
    accountInfo: null,
    isLoading: false,
    error: null
  })

  // Subscribe to Realtime trading service updates
  useEffect(() => {
    const unsubscribe = realtimeTradingService.subscribe((newState) => {
      setState(newState)
    })

    return unsubscribe
  }, [])

  // Subscribe to market data simulator
  useEffect(() => {
    const unsubscribe = marketDataSimulator.subscribe((symbol, marketData) => {
      realtimeTradingService.updateMarketData(symbol, marketData)
    })

    // Start the simulator
    marketDataSimulator.start()

    return () => {
      unsubscribe()
      marketDataSimulator.stop()
    }
  }, [])

  // Actions
  const placeOrder = useCallback(async (order: OrderRequest): Promise<string> => {
    try {
      const orderId = await realtimeTradingService.placeOrder(order)
      return orderId
    } catch (error) {
      console.error('Failed to place order:', error)
      throw error
    }
  }, [])

  const cancelOrder = useCallback(async (orderId: string): Promise<boolean> => {
    try {
      // TODO: Implement cancel order in realtime service
      console.log('Cancel order not implemented yet:', orderId)
      return true
    } catch (error) {
      console.error('Failed to cancel order:', error)
      throw error
    }
  }, [])

  const closePosition = useCallback(async (positionId: string): Promise<boolean> => {
    try {
      await realtimeTradingService.closePosition(positionId)
      return true
    } catch (error) {
      console.error('Failed to close position:', error)
      throw error
    }
  }, [])

  const updatePosition = useCallback(async (update: PositionUpdate): Promise<boolean> => {
    try {
      // Firebase service doesn't have updatePosition yet, return true for now
      console.log('Position update not implemented in Firebase service yet:', update)
      return true
    } catch (error) {
      console.error('Failed to update position:', error)
      throw error
    }
  }, [])

  const updateMarketData = useCallback((symbol: string, data: Partial<MarketData>) => {
    realtimeTradingService.updateMarketData(symbol, data)
  }, [])

  const updateAccountInfo = useCallback((accountInfo: Partial<AccountInfo>) => {
    // Realtime service manages account info automatically, no manual update needed
    console.log('Account info update not needed with Realtime service:', accountInfo)
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Utilities
  const getMarketData = useCallback((symbol: string): MarketData | null => {
    return realtimeTradingService.getMarketData(symbol)
  }, [])

  const getPositionBySymbol = useCallback((symbol: string): Position | null => {
    return state.positions.find(p => p.symbol === symbol) || null
  }, [state.positions])

  const getAccountInfo = useCallback((): AccountInfo | null => {
    return state.accountInfo
  }, [state.accountInfo])

  const getTotalPnL = useCallback((): number => {
    return state.positions.reduce((total, position) => total + position.pnl, 0)
  }, [state.positions])

  const getTotalMargin = useCallback((): number => {
    return state.positions.reduce((total, position) => total + position.margin, 0)
  }, [state.positions])

  const getAvailableBalance = useCallback((): number => {
    return state.accountInfo?.availableBalance || 0
  }, [state.accountInfo])

  const contextValue: TradingContextType = {
    // State
    positions: state.positions,
    orders: state.orders,
    trades: state.trades,
    marketData: state.marketData,
    accountInfo: state.accountInfo,
    isLoading: state.isLoading,
    error: state.error,
    state,

    // Actions
    placeOrder,
    cancelOrder,
    closePosition,
    updatePosition,
    updateMarketData,
    updateAccountInfo,
    clearError,

    // Utilities
    getMarketData,
    getPositionBySymbol,
    getAccountInfo,
    getTotalPnL,
    getTotalMargin,
    getAvailableBalance
  }

  return (
    <TradingContext.Provider value={contextValue}>
      {children}
    </TradingContext.Provider>
  )
}

export function useTrading() {
  const context = useContext(TradingContext)
  if (context === undefined) {
    throw new Error('useTrading must be used within a TradingProvider')
  }
  return context
}

export default TradingContext
